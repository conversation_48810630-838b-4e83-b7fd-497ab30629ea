"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Save,
  ArrowLeft,
  Package,
  Truck,
  DollarSign,
  FileText,
  Calendar,
  Building,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";
import { RawMaterial, RawMaterialSupplier, PurchaseOrder } from "@/types/merchant";
import { convertToZAR, formatZAR } from "@/data/south-african-context";

// Mock material data
const mockMaterial: RawMaterial = {
  id: "rm1",
  merchantId: "m1",
  name: "Premium Black Granite",
  description: "High-quality black granite from Mpumalanga quarries",
  category: "stone",
  unit: "m²",
  costPerUnit: convertToZAR(450.00),
  currentStock: 25.5,
  minimumStock: 10.0,
  maximumStock: 100.0,
  supplierId: "sup1",
  specifications: {},
  storageLocation: "Warehouse A - Section 1",
  isActive: true,
  lastRestocked: new Date("2024-01-15"),
  createdAt: new Date("2023-06-01"),
  updatedAt: new Date("2024-01-15"),
};

// Mock suppliers
const mockSuppliers: RawMaterialSupplier[] = [
  {
    id: "sup1",
    name: "Cape Granite Suppliers",
    contactPerson: "Johan van der Merwe",
    email: "<EMAIL>",
    phone: "+27 21 555 0123",
    address: "15 Industrial Road, Cape Town, 7405",
    rating: 4.8,
    isActive: true,
    paymentTerms: "30 days",
    deliveryTime: 5,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "sup2",
    name: "Johannesburg Stone Works",
    contactPerson: "Sipho Mthembu",
    email: "<EMAIL>",
    phone: "+27 11 555 0456",
    address: "42 Mining Street, Johannesburg, 2001",
    rating: 4.6,
    isActive: true,
    paymentTerms: "15 days",
    deliveryTime: 3,
    createdAt: new Date("2023-03-10"),
    updatedAt: new Date("2024-01-18"),
  },
];

// Mock pending purchase orders
const mockPendingOrders: PurchaseOrder[] = [
  {
    id: "po2",
    merchantId: "m1",
    supplierId: "sup1",
    orderNumber: "PO-2024-002",
    status: "confirmed",
    orderDate: new Date("2024-01-20"),
    expectedDeliveryDate: new Date("2024-01-27"),
    items: [
      {
        id: "poi2",
        materialId: "rm1",
        quantity: 30.0,
        unitCost: convertToZAR(450.00),
        totalCost: convertToZAR(13500.00),
        receivedQuantity: 0,
        notes: "Urgent order for February projects",
      },
    ],
    subtotal: convertToZAR(13500.00),
    tax: convertToZAR(2025.00),
    total: convertToZAR(15525.00),
    notes: "Priority delivery requested",
    createdBy: "admin",
    createdAt: new Date("2024-01-20"),
    updatedAt: new Date("2024-01-20"),
  },
];

export default function ReceiveStock() {
  const params = useParams();
  const router = useRouter();
  const materialId = params.id as string;
  const [material] = useState<RawMaterial>(mockMaterial);

  const [formData, setFormData] = useState({
    quantity: "",
    unitCost: material.costPerUnit.toString(),
    supplierId: material.supplierId,
    orderId: "",
    deliveryDate: new Date().toISOString().split('T')[0],
    reference: "",
    notes: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const quantity = parseFloat(formData.quantity);
    const unitCost = parseFloat(formData.unitCost);
    const totalCost = quantity * unitCost;
    const newStock = material.currentStock + quantity;

    // TODO: Implement actual stock receipt
    console.log("Recording stock receipt:", {
      materialId,
      quantity,
      unitCost,
      totalCost,
      balanceAfter: newStock,
      supplierId: formData.supplierId,
      orderId: formData.orderId || undefined,
      reference: formData.reference,
      notes: formData.notes,
      deliveryDate: new Date(formData.deliveryDate),
    });

    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const handleCancel = () => {
    router.push(`/merchant/raw-materials/${materialId}/stock`);
  };

  const selectedSupplier = mockSuppliers.find(s => s.id === formData.supplierId);
  const selectedOrder = mockPendingOrders.find(o => o.id === formData.orderId);
  const quantity = parseFloat(formData.quantity || "0");
  const unitCost = parseFloat(formData.unitCost || "0");
  const totalCost = quantity * unitCost;
  const newStock = material.currentStock + quantity;
  const isOverMax = newStock > material.maximumStock;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Receive Stock"
        description={`Record incoming stock for ${material.name}`}
        showBackButton
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!formData.quantity || !formData.supplierId}>
              <Save className="mr-2 h-4 w-4" />
              Receive Stock
            </Button>
          </div>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Material Info */}
            <Card>
              <CardHeader>
                <CardTitle>Material Information</CardTitle>
                <CardDescription>
                  Current stock and material details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Material Name</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium">{material.name}</p>
                      <p className="text-sm text-muted-foreground">{material.description}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Current Stock</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="font-medium text-lg">
                        {material.currentStock} {material.unit}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Value: {formatZAR(material.currentStock * material.costPerUnit)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Receipt Details */}
            <Card>
              <CardHeader>
                <CardTitle>Receipt Details</CardTitle>
                <CardDescription>
                  Enter the details of the incoming stock
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Quantity Received *</Label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="quantity"
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder={`0.00 ${material.unit}`}
                        value={formData.quantity}
                        onChange={(e) => handleInputChange("quantity", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unitCost">Unit Cost (ZAR) *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="unitCost"
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder="0.00"
                        value={formData.unitCost}
                        onChange={(e) => handleInputChange("unitCost", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="supplierId">Supplier *</Label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                      <Select
                        value={formData.supplierId}
                        onValueChange={(value) => handleInputChange("supplierId", value)}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue placeholder="Select supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockSuppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id}>
                              {supplier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orderId">Purchase Order (Optional)</Label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10" />
                      <Select
                        value={formData.orderId}
                        onValueChange={(value) => handleInputChange("orderId", value)}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue placeholder="Select purchase order" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No purchase order</SelectItem>
                          {mockPendingOrders
                            .filter(order => order.supplierId === formData.supplierId)
                            .map((order) => (
                              <SelectItem key={order.id} value={order.id}>
                                {order.orderNumber} - {formatZAR(order.total)}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="deliveryDate">Delivery Date *</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="deliveryDate"
                        type="date"
                        value={formData.deliveryDate}
                        onChange={(e) => handleInputChange("deliveryDate", e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reference">Reference/Invoice Number</Label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="reference"
                        placeholder="e.g., INV-2024-001, DEL-001"
                        value={formData.reference}
                        onChange={(e) => handleInputChange("reference", e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Delivery Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Any notes about the delivery, condition, quality, etc..."
                    value={formData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                    rows={3}
                  />
                </div>

                {selectedOrder && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <h4 className="font-medium text-blue-900 mb-2">Purchase Order Details</h4>
                    <div className="grid gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-blue-700">Order Number:</span>
                        <span className="font-medium">{selectedOrder.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-blue-700">Ordered Quantity:</span>
                        <span className="font-medium">
                          {selectedOrder.items[0]?.quantity} {material.unit}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-blue-700">Expected Cost:</span>
                        <span className="font-medium">
                          {formatZAR(selectedOrder.items[0]?.unitCost || 0)}/{material.unit}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stock Impact */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Impact</CardTitle>
                <CardDescription>
                  How this receipt will affect stock levels
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Current Stock</span>
                  <span className="font-medium">
                    {material.currentStock} {material.unit}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Receiving</span>
                  <span className="font-medium text-green-600">
                    +{formData.quantity || "0"} {material.unit}
                  </span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">New Stock Level</span>
                    <span className={`font-bold ${isOverMax ? 'text-red-600' : 'text-green-600'}`}>
                      {newStock.toFixed(2)} {material.unit}
                    </span>
                  </div>
                  {isOverMax && (
                    <div className="flex items-center gap-1 mt-1">
                      <AlertTriangle className="h-3 w-3 text-red-600" />
                      <span className="text-xs text-red-600">Exceeds maximum stock level</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">New Stock Value</span>
                  <span className="font-medium">
                    {formatZAR(newStock * unitCost)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Cost Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Quantity:</span>
                  <span className="font-medium">{formData.quantity || "0"} {material.unit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Unit Cost:</span>
                  <span className="font-medium">{formatZAR(unitCost)}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Total Cost:</span>
                    <span className="font-bold text-lg">{formatZAR(totalCost)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Supplier Info */}
            {selectedSupplier && (
              <Card>
                <CardHeader>
                  <CardTitle>Supplier Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Company:</span>
                    <span className="font-medium truncate ml-2">{selectedSupplier.name}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Contact:</span>
                    <span className="font-medium truncate ml-2">{selectedSupplier.contactPerson}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Payment Terms:</span>
                    <span className="font-medium">{selectedSupplier.paymentTerms}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Rating:</span>
                    <span className="font-medium">{selectedSupplier.rating}/5.0</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Guidelines */}
            <Card>
              <CardHeader>
                <CardTitle>Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-muted-foreground">
                <p>• Verify quantities before recording receipt</p>
                <p>• Check material quality and condition</p>
                <p>• Link to purchase orders when applicable</p>
                <p>• Include delivery reference numbers</p>
                <p>• Note any discrepancies or issues</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
